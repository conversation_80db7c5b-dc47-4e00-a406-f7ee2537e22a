import React, { useRef, lazy, Suspense, useEffect } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd } from '@src/common/utils'
import { message } from 'antd/es'
import { getCurrentTab } from '@src/sidepanel/utils'
import { useTranslate } from '@src/common/hooks'
import GuidePage from './GuidePage'
import FooterVersion from './FooterVersion'
import Loading from './Loading'
import HistoryListItem from './HistoryListItem'
import ChatUI from '@ht/chatui'
import '@ht/chatui/dist/index.css'
import './style.less'
import { MessageType, EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'


interface AiChatProps {
  selectedText?: string;
  textOperation?: string;
}

export default ({ selectedText, textOperation }: AiChatProps) => {
  const chatUiRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  useTranslate(chatUiRef)
  const actions = getAllActions()
  // 初始化日志
  const initLog = () => {
    init({
      uuid: 'anonymous_user',
      from: 'HtscAiExtension',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        // 项目信息配置，数据是上报到数智中台，需要去数智中台申请一个项目（product_id和product_name）
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test', // 上报环境
      },
    })
  }

  const handleTranslateCurrentPage = async () => {
    try {

      const currentTab = await getCurrentTab(messageApi)
      console.log('开始翻译页面...', currentTab);
      if (!currentTab) {
        return
      }
      chrome.tabs.sendMessage(currentTab.id, {
        type: MessageType.START_TRANSLATE,
      })
    } catch (error) {
      console.error('翻译页面失败:', error);
    }

  }

  const handleSummaryCurrentPage = async () => {
    const currentTab = await getCurrentTab(messageApi)
    const html_content = await getPageContent()

    const plainText = htmlToPlainText(html_content)

    chatUiRef.current.chatContext.onSend('text', `网页内容：${plainText}`, {
      agentId: 'summary',
    }, [{
      type: 'citeweb',
      url: currentTab.url,
    }])
  }


  useEffect(() => {
    initLog()
  }, [])


  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: {
      type: EFloatButtonActionType
    }) => {
      console.log('Sidepanel received message:', message);
      if (message.type === EFloatButtonActionType.Summary) {
        handleSummaryCurrentPage()
      } else if (message.type === EFloatButtonActionType.Translate) {
        handleTranslateCurrentPage()
      }
    }; chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, []);

  type TRenderWelcomeReturnType = React.ReactNode &
    React.ForwardRefExoticComponent<any>

  const onReportLog = (params) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }

  async function extractPageContent(currentTab: any): Promise<string> {
    return new Promise((resolve) => {
      chrome.scripting
        .executeScript({
          target: { tabId: currentTab.id },
          func: () => {
            // 非递归、深度优先遍历获取所有的dom元素
            const DFSDomTraversal = (root) => {
              if (!root) return

              const arr = []
              const queue = [root]
              let node = queue.shift()

              while (node) {
                arr.push(node)
                const childLen = node.children.length
                if (childLen) {
                  for (let i = childLen - 1; i >= 0; i--) {
                    queue.unshift(node.children[i])
                  }
                }
                node = queue.shift()
              }
              return arr
            }

            /**
             * 获取dom结构
             */
            function copyRootDomContent(root, copyRoot) {
              const rootDom = DFSDomTraversal(root)
              const copyRootDom = DFSDomTraversal(copyRoot)
              const removeElement = (originElement, eleIndex) => {
                const copyElement = copyRootDom[eleIndex]

                // 要过滤的标签列表
                const FILTER_TAGS = [
                  'canvas',
                  'svg',
                  'img',
                  'video',
                  'audio',
                  'iframe',
                  'embed',
                  'meta',
                  'link',
                  'script',
                  'style',
                  'hr',
                ]

                // 检查元素是否隐藏
                function isHidden(node) {
                  if (!(node instanceof Element)) return false

                  const style = window.getComputedStyle(node)
                  return (
                    style.display === 'none' ||
                    style.visibility === 'hidden' ||
                    style.opacity === '0' ||
                    node.hasAttribute('hidden')
                  )
                }

                if (
                  FILTER_TAGS.includes(originElement.tagName?.toLowerCase())
                ) {
                  copyElement.remove()
                } else if (isHidden(originElement)) {
                  copyElement.remove()
                }
              }

              rootDom.forEach(removeElement)
              return new XMLSerializer().serializeToString(copyRoot)
            }

            const copyHtmlContent = () => {
              console.log('--------------copyHtmlContent-------------------')
              console.log(document.title)
              const root = document.documentElement
              const cloneRoot = root.cloneNode(true)
              const content = copyRootDomContent(root, cloneRoot)

              return content.replace(/\s+/g, ' ')
            }
            return copyHtmlContent()
          },
        })
        .then((result) => {
          resolve(result[0].result)
        })
    })
  }

  const getPageContent = async () => {
    const currentTab = await getCurrentTab(messageApi)
    const htmlContent = await extractPageContent(currentTab)
    return htmlContent
  }
  // 将 HTML 字符串转换为纯文本（不包含任何标签）
  const htmlToPlainText = (html: string): string => {
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(html || '', 'text/html')
      doc.querySelectorAll('script, style, noscript, canvas, svg, iframe, meta, link, embed').forEach((el) => el.remove())
      const rawText = (doc.body?.textContent ?? doc.documentElement?.textContent ?? '')
      return rawText
        .replace(/\u00a0/g, ' ')
        .replace(/[\t\r\f]+/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()
    } catch (e) {
      // 回退方案：简单移除标签
      return (html || '')
        .replace(/<[^>]*>/g, ' ')
        .replace(/\u00a0/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()
    }
  }

  const config = {
    //租户id：表示当前系统
    appId: 'web-assistant',

    //用户id：代表当前系统唯一用户id
    userId: 'anonymous_user',

    requests: {
      baseUrl() {
        return ``
      },
      init: {
        // type: 'http',//请求链路类型：'tcp'/'http',如果是tcp则需要传aciton字段，默认http
        // action:'27006',//移动端如果走tcp，需要传接口请求action号
        // paramsKey：'MS__REQUEST__PAYLOAD'，//移动端如果走tcp，app包裹参数，不通app不一样，聊他传‘MS__REQUEST__PAYLOAD’
        url: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
        // url: '/aorta/operation/api/desktop/ai/agent/tec/all/AortaAIAgentQuery/queryWelcomeInfo',

        headers: { empid: '002332', token: 'token', 'deviceId': 'deviceId' },//请求header，可选
        // requestTransfer: (input: object) => { //同步写法
        // requestTransfer:async (input: object) => { //支持异步写法
        //   const parsedInput = {
        //     ...input,
        //     customerInput1: '123',
        //   }
        //   return parsedInput;
        // },
        // responseTransfer: (output: object) => {
        //   const parsedOutput = {
        //     ...output,
        //     customeroutput1: '123',
        //   }
        //   return parsedOutput;
        // },
      },


      //问答接口
      send: {
        url: 'http://webassist.sit.sass.htsc/chat/workflow/chrome',
        createConversationUrl: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
        isAibag: true,
        stream: true,
        messageInterval: 50,
        // async requestTransfer(params) {
        //   if (isPagesummary) {
        //     params.inputs = {
        //       HTML_CONTENT: await getPageContent(),
        //     }
        //   }
        //   return params
        // },
      },
      //查询历史详情接口
      history: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/getHistoryMessages',
      },
      //点赞点踩接口
      score: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/feedback',
      },
      //停止生成接口
      stop: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/interruptSession',
      },

      // 历史会话列表
      historyConversation: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/getHistorySessions',
      },

      deleteHistoryConversation: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/deleteSession',
      },

      deleteMessage: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/deleteMessage',
      },

      // 创建新会话接口
      createConversation: {
        url: 'http://10.102.92.209:9607/ai/orchestration/session/createSession',
      },

    },
  }

  const composerConfig = {
    // aboveNode: <AboveNode chatUiRef={chatUiRef} />,
    // placeholder: getPlaceholder(),
    quoteOperations: {
      citetext: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译',// 发送时的命令 --》 拼装
        agentId: 'translate',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      },
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '总结',// 文案
        question: '总结',// 发送时的命令 --》 拼装
        agentId: 'summary',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '扩写',// 文案
        question: '扩写',// 发送时的命令 --》 拼装
        agentId: 'text-expander',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '缩写',// 文案
        question: '缩写',// 发送时的命令 --》 拼装
        agentId: 'text-condenser',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '润色',// 文案
        question: '润色',// 发送时的命令 --》 拼装
        agentId: 'text-polisher',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }, {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '修正拼写和语法',// 文案
        question: '修正拼写和语法',// 发送时的命令 --》 拼装
        agentId: 'grammar-corrector',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
      citeweb: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],

      image: [
        {
          disabled: false,//是否禁用
          icon: '',// 图标
          label: '翻译',// 文案
          question: '翻译翻译',// 发送时的命令 --》 拼装
          agentId: 'agent1',//发送时可指定智能体ID --》拼装
          customRender: '',//自定义渲染
        }
      ],

      file: [{
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '翻译',// 文案
        question: '翻译翻译',// 发送时的命令 --》 拼装
        agentId: 'agent1',//发送时可指定智能体ID --》拼装
        customRender: '',//自定义渲染
      }],
    },
    skill: [
      {
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '智能翻译',// 文案
        question: '翻译上面文字',// 发送时的命令
        agentId: '1',//发送时可指定智能体ID
        children: [
          {
            key: 'translate',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: 'AI翻译',// 文案
            question: 'AI翻译',// 发送时的命令
            agentId: 'translate',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'translatePage',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '翻译此页面',// 文案
            question: '翻译此页面',// 发送时的命令
            agentId: 'translate',//发送时可指定智能体ID
            customRender: '',//自定义渲染
            onClick: handleTranslateCurrentPage
          }
        ],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log(1111),// 技能点击回调 -》 由AiChat统一写入Composer
      },
      {
        key: 'summary',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '网页总结',// 文案
        question: '网页总结',// 发送时的命令
        agentId: 'summary',//发送时可指定智能体ID
        customRender: '',//自定义渲染
        onClick: handleSummaryCurrentPage,
      },
      {
        key: 'more',// 必传，唯一标识
        disabled: false,//是否禁用
        icon: '',// 图标
        label: '更多',// 文案
        question: '',// 发送时的命令
        agentId: '',//发送时可指定智能体ID
        children: [
          {
            key: 'text-condenser',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '缩写',// 文案
            question: '缩写',// 发送时的命令
            agentId: 'text-condenser',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'text-expander',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '扩写',// 文案
            question: '扩写',// 发送时的命令
            agentId: 'text-expander',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'text-polisher',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '改写',// 文案
            question: '改写',// 发送时的命令
            agentId: 'text-polisher',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          },
          {
            key: 'grammar-corrector',// 必传，唯一标识
            disabled: false,//是否禁用
            icon: '',// 图标
            label: '修订语法与拼写',// 文案
            question: '修订语法与拼写',// 发送时的命令
            agentId: 'grammar-corrector',//发送时可指定智能体ID
            customRender: '',//自定义渲染
          }
        ],//折叠子项,有子项时，点击会展开子项
        // expandIcon?: string,// 折叠图标
        // customRender?: React.ReactNode,//自定义渲染
        onClick: () => console.log('more clicked'),// 技能点击回调 -》 由AiChat统一写入Composer
      }
    ],

  }

  return (
    <Suspense fallback={<Loading />}>
      <ChatUI
        navbar={{
          showLogo: false,
          showCloseButton: false,
          title: '',
        }}
        ref={chatUiRef}
        config={config}
        actions={actions}
        // historyConversation={{
        //   renderListItem(props) {
        //     return <HistoryListItem {...props} />
        //   },
        // }}
        renderWelcome={(props) =>
          (<GuidePage {...props} />) as TRenderWelcomeReturnType
        }
        onReportLog={onReportLog}
        inputOptions={{
          minRows: 2,
        }}
        composerConfig={composerConfig}
        renderFooterVersion={() => <FooterVersion />}
        showStopAnswer={true}
        showToken={false} // 不展示消耗的token数量
        showHallucination={false} // 不展示合规话术
      />
      {contextHolder}
    </Suspense>
  )
}
